//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Bookings screen - My bookings are displayed', {
    tag: '@serial',
    annotation: { type: 'coa', description: 'Bookings screen - My bookings are displayed' }
}, async ({ page, loginPage, searchResultsPage, newBookingModalPage, myBookingsPage }) => {

    test.slow(); //Sets the timeout to triple the project timeout.

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    // Create multiple bookings for testing display and ordering
    const timeNow = dayjs();
    const bookings = [
        {
            startTime: timeNow.add(1, 'hour'),
            endTime: timeNow.add(1, 'hour').add(30, 'minutes'),
            title: `First Booking - Made by ${standard_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`,
            resource: 'Basement Room 2'
        },
        {
            startTime: timeNow.add(3, 'hour'),
            endTime: timeNow.add(3, 'hour').add(45, 'minutes'),
            title: `Second Booking - Made by ${standard_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`,
            resource: 'Ground Floor Room 1'
        },
        {
            startTime: timeNow.add(1, 'day').add(2, 'hour'),
            endTime: timeNow.add(1, 'day').add(2, 'hour').add(1, 'hour'),
            title: `Tomorrow Booking - Made by ${standard_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`,
            resource: 'Ground Floor Room 2'
        },
        {
            startTime: timeNow.add(2, 'day').add(1, 'hour'),
            endTime: timeNow.add(2, 'day').add(1, 'hour').add(1, 'hour'),
            title: `2 Days in future Booking - Made by ${standard_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`,
            resource: 'Basement Room 7 - Facilities'
        }
    ];

    await test.step('Create multiple bookings for testing display', async () => {
        await page.getByRole('button', { name: 'Search' }).click();


        for (let i = 0; i < bookings.length; i++) {
            const booking = bookings[i];

            await test.step(`Create booking ${i + 1}: ${booking.title}`, async () => {
                await searchResultsPage.bookResource(booking.resource);
                //set date
                await newBookingModalPage.setStartDate(booking.startTime, projectViewport.width);
                await newBookingModalPage.setStartTime(booking.startTime);
                await newBookingModalPage.setEndTime(booking.endTime);
                await newBookingModalPage.setTitle(booking.title);
                await newBookingModalPage.completeBooking();

                // Navigate back to search for next booking (except for last booking)
                if (i < bookings.length - 1) {
                    await newBookingModalPage.makeAnotherBookingButton.click();
                }
            });
        }

        // Close the modal after last booking
        //if small screen, close the modal
        if (projectViewport.width < 768) {
            await newBookingModalPage.closeButton.click();
        } else {
            await newBookingModalPage.closeBookingConfirmationButton.click();
        }
    });

    await test.step('Navigate to Bookings page', async () => {
        if (projectViewport.width < 1024) {
            await page.getByRole('button', { name: 'Menu Access menu options' }).click();
        }
        await page.getByRole('link', { name: 'Menu item: Bookings' }).click();
        await expect(page).toHaveURL('/neo/my-bookings');
    });

    await test.step('Verify location filter is present in the left bar', async () => {
        // Check for location filter element - this should be visible in the left sidebar for large view only
        // and date dddd dd MMMM is visible in the main body
        await expect(page.getByRole('heading', { name: `Today, ${bookings[0].startTime.format('dddd DD MMMM')}` })).toBeVisible();
        await expect(page.getByRole('heading', { name: `Tomorrow, ${bookings[2].startTime.format('dddd DD MMMM')}` })).toBeVisible();
        await expect(page.getByRole('heading', { name: `${bookings[3].startTime.format('dddd DD MMMM')}` })).toBeVisible();


        if (projectViewport.width < 1024) {
            // Desktop/tablet view - click to open filter options
            await page.getByRole('button', { name: 'Filter' }).click();
            // Verify location filter is visible
            await expect(page.getByRole('button', { name: 'Location' })).toBeVisible();
            // and can be expanded
            await page.getByRole('button', { name: 'Wales' }).first().click();
            await page.getByRole('button', { name: 'Location' }).locator('..').locator('..').getByRole('button', { name: 'Test Building 1' }).first().click();
            await expect(page.getByRole('button', { name: 'Basement' })).toBeVisible();
            await expect(page.getByRole('button', { name: 'Ground Floor' })).toBeVisible();
            await page.getByRole('button', { name: 'Close panel' }).click();
        } else {
            await expect(page.getByRole('button', { name: 'Location' })).toBeVisible();
            await page.getByRole('button', { name: 'Location' }).locator('..').locator('..').getByRole('button', { name: 'Wales' }).first().click();
            await page.getByRole('button', { name: 'Location' }).locator('..').locator('..').getByRole('button', { name: 'Test Building 1' }).first().click();
            await expect(page.getByRole('button', { name: 'Location' }).locator('..').locator('..').getByRole('button', { name: 'Basement' })).toBeVisible();
            await expect(page.getByRole('button', { name: 'Location' }).locator('..').locator('..').getByRole('button', { name: 'Ground Floor' })).toBeVisible();
        }
    });

    await test.step('Verify booking cards are displayed in main body', async () => {
        // Verify all created bookings are visible
        for (const booking of bookings) {

            const bookingElement = await myBookingsPage.findBooking(
                booking.startTime,
                booking.title,
                projectViewport.width
            );
            await expect(bookingElement).toBeVisible();
        }
    });

    await test.step('Verify bookings are displayed in chronological order from earliest to latest', async () => {
        await page.waitForLoadState('networkidle');
        // Get all booking cards
        let bookingCards;
        if (projectViewport.width < 768) {
            bookingCards = page.locator('.text-left.md\\:hidden.text-subtitle.border-border-decoration.shadow-2.flex.flex-col');
        } else {
            bookingCards = page.locator('.hidden.md\\:flex.text-subtitle.border-border-decoration');
        }
        expect(await bookingCards.nth(0).getByText(bookings[0].title, { exact: false }).isVisible()).toBe(true);
        expect(await bookingCards.nth(1).getByText(bookings[1].title, { exact: false }).isVisible()).toBe(true);
        expect(await bookingCards.nth(2).getByText(bookings[2].title, { exact: false }).isVisible()).toBe(true);
        expect(await bookingCards.nth(3).getByText(bookings[3].title, { exact: false }).isVisible()).toBe(true);


    });

    await test.step('Verify bookings are grouped by day', async () => {


        // Verify today's heading is present
        await expect(page.getByRole('heading', { name: `Today, ${timeNow.format('dddd DD MMMM')}` })).toBeVisible();

        // Verify tomorrow's heading is present (since we created a booking for tomorrow)
        await expect(page.getByRole('heading', { name: `Tomorrow, ${timeNow.add(1, 'day').format('dddd DD MMMM')}` })).toBeVisible();

        // Verify bookings appear under correct day headings
        const todaySection = await page.getByRole('heading', { name: `Today, ${bookings[0].startTime.format('dddd DD MMMM')}` }).locator('..');
        const tomorrowSection = await page.getByRole('heading', { name: `Tomorrow, ${bookings[2].startTime.format('dddd DD MMMM')}` }).locator('..');
        const futureSection = await page.getByRole('heading', { name: `${bookings[3].startTime.format('dddd DD MMMM')}` }).locator('..');

        // Check today's bookings are under today's heading
        await expect(todaySection.locator(`:visible`).getByText(bookings[0].title, { exact: false }).last()).toBeVisible();

        await expect(tomorrowSection.locator(`:visible`).getByText(bookings[2].title, { exact: false }).last()).toBeVisible();

        await expect(futureSection.locator(`:visible`).getByText(bookings[3].title, { exact: false }).last()).toBeVisible();

    });

    await test.step('Clean up - Cancel all created bookings', async () => {
        // Cancel all bookings in reverse order
        for (let i = bookings.length - 1; i >= 0; i--) {
            const booking = bookings[i];
            await myBookingsPage.cancelBooking(
                booking.startTime,
                booking.endTime,
                booking.title,
                false, // cancelAllInstances
                projectViewport.width
            );
            // Wait for the booking to be removed from the list
            await myBookingsPage.waitForCancelDialogClose();
        }
    });
});


