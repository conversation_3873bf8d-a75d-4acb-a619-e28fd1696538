import { expect } from '@playwright/test';
import dayjs from 'dayjs';
export class MyBookingsPage {
  constructor(page) {
    this.page = page;
    this.pageHeading = page.getByRole('heading', { name: /Today, / });
    this.editBookingButton = (time) => page.getByRole('button', { name: `Edit booking ${time}` });
    this.cancelBookingButton = (time) => page.getByRole('button', { name: `Cancel booking ${time}` });
    this.mobileBookingButton = (time, title) => page.getByRole('button', { name: `${time} ${title}` });
    this.mobileCancelButton = page.getByRole('button', { name: 'Cancel booking' });
    this.cancelSingleButton = page.getByRole('button').filter({ hasText: 'Cancel just this booking' });
    this.cancelAllButton = page.getByRole('button').filter({ hasText: 'Cancel all remaining bookings in this series' });
    this.confirmCancelButton = page.getByRole('button', { name: 'Cancel booking' }).last();
    this.cancelDialogTitle = page.getByRole('heading', { name: 'Cancel booking' });
    this.cancelDialogMessage = page.getByText('This action cannot be undone and will make the room available for others to book.');
    this.cancelDialogCloseButton = page.getByRole('button', { name: 'Close' });
  }

  async goto() {
    await this.page.goto('/neo/my-bookings');
  }


  getTruncatedTitle(title) {
    // Extract the first part of the title before any special characters
    // This helps with matching truncated titles in medium/small views
    const firstPart = title.split(' - ')[0];
    return firstPart;
  }

  /**
   * Find a booking by time and title
   * @param {dayjs.Dayjs} startTime - The booking start time
   * @param {string} title - The booking title
   * @param {number} viewportWidth - The current viewport width
   * @returns {Promise<import('@playwright/test').Locator>} - The booking element
   */
  async findBooking(startTime, title, viewportWidth) {
    // Wait for the page to stabilize
    await this.page.waitForLoadState('networkidle');

    if (viewportWidth < 768) {
      // Mobile view - use truncated title for mobile view
      const truncatedTitle = this.getTruncatedTitle(title);

      // Try to find the booking using the truncated title
      const mobileBooking = this.page.getByText(truncatedTitle, { exact: false }).first();
      await mobileBooking.waitFor({ state: 'visible', timeout: 10000 });

      return mobileBooking;
    } else if (viewportWidth < 1024) {
      // Medium view - use truncated title
      const truncatedTitle = this.getTruncatedTitle(title);
      await this.page.getByText(truncatedTitle, { exact: false }).last().waitFor({ timeout: 10000 });

      // Now look for the booking row or card
      const bookingElement = this.page.getByText(truncatedTitle, { exact: false }).last();

      return bookingElement;
    } else {
      // Desktop view - more complex as the title and time might be in separate elements
      const timeFormat = startTime.format('h:mm A');

      // Look for any element containing the booking title
      // This is more reliable than looking for specific card elements
      await this.page.getByText(title, { exact: false }).last().waitFor({ timeout: 10000 });

      // Now look for the booking row or card
      const bookingElement = this.page.getByText(title, { exact: false }).last();

      return bookingElement;
    }
  }

  /**
   * Edit a booking
   * @param {string} title - The booking title
   * @param {number} viewportWidth - The current viewport width
   */  async editBooking(dateStartTime, endTime, title, viewportWidth) {
    await this.page.waitForLoadState('networkidle');
    const truncatedTitle = this.getTruncatedTitle(title);

    if (viewportWidth < 768) {
      // For mobile/tablet, use the truncated title and check within the content div
      const bookingHeading = this.page.locator('#content-div')
        .getByRole('heading', { name: dateStartTime.format('dddd DD MMMM'), exact: false })
        .locator('..')
        .getByRole('heading', { name: truncatedTitle, exact: false })
        .first();

      await bookingHeading.waitFor({ state: 'visible', timeout: 10000 });
      await bookingHeading.click()


    } else {
      // For desktop, find the booking section and click edit
      const bookingElement = this.page
        .getByText(dateStartTime.format('dddd DD MMMM'), { exact: false })
        .locator('..')
        .getByText(title, { exact: true })
        .last();

      await bookingElement.waitFor({ state: 'visible', timeout: 10000 });
      await bookingElement.click();
      const editButton = bookingElement.locator('..').locator('..').locator('..').getByRole('button', { name: 'Edit booking' });
      await editButton.click();
    }
  }

  /**
   * Cancel a booking - handles both standard and repeat bookings
   */
  async cancelBooking(dateStartTime, endTime, title, cancelAllInstances = false, viewportWidth) {
    const bookingElement = await this.openCancelDialog(dateStartTime, title, viewportWidth);

    // Check if this is a repeat booking by seeing if options exist
    const hasRepeatOptions = await this.cancelSingleButton.isVisible({ timeout: 1000 })
      .catch(() => false);

    // Only try to use repeat options if they exist
    if (hasRepeatOptions) {
      if (cancelAllInstances) {
        await this.cancelAllButton.click();
      }
    }

    // Complete cancellation
    await this.confirmCancelButton.waitFor({ state: 'visible', timeout: 5000 });
    await this.confirmCancelButton.click();
    //wait for loading to not be visible
    await expect(this.page.locator('.loading')).toBeHidden();
    await expect(this.page.locator('div.animate-spin-slow')).toBeHidden();
    //wait for dialog to close
    await expect(this.page.getByRole('dialog')).toBeHidden();
    

    // Verify removal
    await this.verifyBookingDoesNotExist(dateStartTime, title, viewportWidth);
  }

  /**
   * Open cancel dialog without completing cancellation
   */
  async openCancelDialog(dateStartTime, title, viewportWidth) {
    await this.page.waitForLoadState('networkidle');
    const truncatedTitle = this.getTruncatedTitle(title);

    if (viewportWidth < 1200) {
      // For mobile and medium views, use truncated title
      const bookingHeading = this.page.locator('#content-div')
        .getByRole('heading', { name: dateStartTime.format('dddd DD MMM'), exact: false })
        .locator('..')
        .getByRole('heading', { name: truncatedTitle, exact: false })
        .first();

      await bookingHeading.waitFor({ state: 'visible', timeout: 10000 });

      if (viewportWidth < 768) {
        // Mobile specific handling
        await bookingHeading.click();
        await this.mobileCancelButton.click();
      } else {
        // Medium view handling
        const cancelButton = bookingHeading.locator('..').locator('..').locator('..').getByRole('button', { name: 'Cancel booking' });
        await cancelButton.click();
      }
    } else {
      // Desktop view - use full title
      const bookingElement = this.page
        .getByRole('heading', { name: dateStartTime.format('dddd DD MMM'), exact: false })
        .locator('..')
        .getByText(truncatedTitle, { exact: false })
        .last();

      await bookingElement.waitFor({ state: 'visible', timeout: 10000 });
      await bookingElement.locator('..').locator('..').locator('..').locator('..').getByRole('button', { name: 'Cancel booking' }).click();
    }
  }

  /**
   * Verify a booking does not exist
   */
  async verifyBookingDoesNotExist(dateStartTime, title, viewportWidth) {
    await this.page.waitForLoadState('networkidle');
    const truncatedTitle = this.getTruncatedTitle(title);

    if (viewportWidth < 1200) {
      const bookingHeading = this.page.locator('#content-div')
        .getByRole('heading', { name: dateStartTime.format('dddd DD MMM'), exact: false })
        .locator('..')
        .getByRole('heading', { name: truncatedTitle, exact: false })
        .first();
      await expect(bookingHeading).toHaveCount(0);
    } else {
      const bookingElement = this.page
        .getByText(dateStartTime.format('dddd DD MMM'))
        .locator('..')
        .getByText(title, { exact: true })
        .last();
      await expect(bookingElement).toHaveCount(0);
    }
  }

  /**
   * Wait for cancel dialog to close and check if booking still exists
   */
  async waitForCancelDialogClose(dateStartTime, title, viewportWidth) {
    // Wait for dialog to close
    await expect(this.cancelDialogTitle).toBeHidden();
    await this.page.waitForLoadState('networkidle');
    await expect(this.page.locator('.loading')).toBeHidden();
    await expect(this.page.locator('div.animate-spin-slow')).toBeHidden();
  }

  /**
   * Verify a booking exists on the current day
   * @param {dayjs.Dayjs|string} startTimeOrTitle - Either the booking start time or the booking title
   * @param {dayjs.Dayjs|number} endTimeOrViewportWidth - Either the booking end time or the viewport width
   * @param {string|undefined} [title] - The booking title (if first param is start time)
   * @param {number|undefined} [viewportWidth] - The viewport width (if first param is start time)
   */  async verifyBookingExists(dateStartTime, endTime, title, viewportWidth) {
    await this.page.waitForLoadState('networkidle');
    const truncatedTitle = this.getTruncatedTitle(title);

    if (viewportWidth < 1200) {
      // For mobile/tablet, use the truncated title and check within the content div
      const bookingHeading = this.page.locator('#content-div').getByRole('heading', { name: dateStartTime.format('dddd DD MMM'), exact: false }).locator('..')
        .getByRole('heading', { name: truncatedTitle, exact: false })
        .first();

      await bookingHeading.waitFor({ state: 'visible', timeout: 10000 });
      const statusElement = bookingHeading.locator('..').locator('..').getByText('BOOKED');
      await expect(statusElement).toBeVisible({ timeout: 5000 });
    } else {
      // For desktop, try full title first
      const bookingElement = this.page.getByText(dateStartTime.format('dddd DD MMM')).locator('..').getByText(truncatedTitle, { exact: false }).last();
      await bookingElement.waitFor({ state: 'visible', timeout: 10000 });
      const statusElement = bookingElement.locator('..').locator('..').getByText('BOOKED');
      await expect(statusElement).toBeVisible({ timeout: 5000 });
    }
  }
}



